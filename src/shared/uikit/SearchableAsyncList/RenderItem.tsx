import React from 'react';
import BaseButton from 'shared/uikit/Button/BaseButton';

interface RenderListItemProps<T extends { id: string | number }> {
  item: T;
  index: number;
  inputValue: string;
  isItemSelected: (item: T) => boolean;
  handleItemSelect: (item: T) => void;
  listItemClassName?: string;
  renderItem: (args: {
    item: T;
    isSelected: boolean;
    index: number;
    text?: string;
  }) => React.ReactNode;
}

const RenderListItem = <T extends { id: string | number }>({
  item,
  index,
  inputValue,
  isItemSelected,
  handleItemSelect,
  listItemClassName,
  renderItem,
}: RenderListItemProps<T>) => (
  <BaseButton
    key={item.id}
    aria-selected={isItemSelected(item)}
    onClick={() => handleItemSelect(item)}
    className={listItemClassName}
  >
    {renderItem({
      item,
      isSelected: isItemSelected(item),
      index,
      text: inputValue,
    })}
  </BaseButton>
);

export default RenderListItem;
