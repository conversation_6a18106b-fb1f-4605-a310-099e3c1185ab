import ViewPortList from '@shared/uikit/ViewPortList';
import { useQuery } from '@tanstack/react-query';
import { getJobTrackingEmails } from '@shared/utils/api/jobs';
import DividerVertical from '@shared/uikit/Divider/DividerVertical';
import Flex from '@shared/uikit/Flex';
import Icon from '@shared/uikit/Icon';
import Typography from '@shared/uikit/Typography';
import InfoCard from 'shared/components/Organism/Objects/Common/InfoCard';
import React, { useCallback } from 'react';
import EmptySearchResult from '@shared/components/Organism/EmptySearchResult';
import { QueryKeys } from '@shared/utils/constants';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { CandidateFormData } from '@shared/types/candidates';

interface EmailItem {
  id: string;
  title: string;
  email: string;
  time: string;
}

interface CandidateEmailsManagerProps {
  candidate: CandidateFormData;
}
const CandidateEmailsManager = ({ candidate }: CandidateEmailsManagerProps) => {
  const { t } = useTranslation();

  const { data, isLoading } = useQuery<EmailItem[]>({
    queryKey: [QueryKeys.jobTrackingEmails, candidate.id],
    queryFn: () => getJobTrackingEmails(candidate.id || ''),
  });

  const MemoizedEmailItem = useCallback(
    (index: number, email: EmailItem) => (
      <InfoCard
        key={email.id}
        onClick={() => {}}
        icon="envelope"
        titleProps={{
          font: '700',
          color: 'smoke_coal',
          size: 15,
          height: 18,
          lineNumber: 1,
        }}
        title={email.title}
        value={
          <Flex className="!flex-row">
            <Typography
              color="secondaryDisabledText"
              size={12}
              height={18}
              lineNumber={1}
              className="flex-1"
            >
              {email.email}
            </Typography>
            <DividerVertical className="!mx-8" />
            <Typography
              color="secondaryDisabledText"
              size={12}
              height={18}
              lineNumber={1}
              className="flex-1"
            >
              {email.time}
            </Typography>
          </Flex>
        }
        valueProps={{ color: 'primaryText' }}
      >
        <Flex className="items-center justify-center h-full ml-12">
          <Icon name="chevron-right" type="far" size={13} />
        </Flex>
      </InfoCard>
    ),
    []
  );

  if (!isLoading && !data?.length) {
    return (
      <EmptySearchResult
        title={t('no_emails_found')}
        sectionMessage={t('no_emails_found_desc')}
        className="flex-1"
      />
    );
  }

  return (
    <ViewPortList
      useRelativeScroller
      style={{ height: '100%' }}
      data={data}
      increaseViewportBy={200}
      itemContent={MemoizedEmailItem}
      className="mt-20"
      components={{
        List,
      }}
    />
  );
};

export default CandidateEmailsManager;

const List = (props: React.HTMLProps<HTMLDivElement>) => (
  <div
    {...props}
    className="flex flex-col gap-4 -m-8 pt-20 mx-[-16px] sm:mx-[-10px]"
  />
);
List.displayName = 'List';
