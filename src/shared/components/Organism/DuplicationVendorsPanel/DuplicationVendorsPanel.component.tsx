import React, { useState, type MouseEvent as RMouseEvent } from 'react';

import useTranslation from 'shared/utils/hooks/useTranslation';
import {
  useGlobalDispatch,
  useGlobalState,
} from 'shared/contexts/Global/global.provider';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import FixedRightSideModal from 'shared/uikit/Modal/FixedRightSideModalDialog';
import DuplicationVendorItem from '@shared/components/Organism/DuplicationVendorsPanel/DuplicationVendorItem';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import ModalFooter from '@shared/uikit/Modal/ModalFooter';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import useOpenConfirm from '@shared/uikit/Confirmation/useOpenConfirm';
import Typography from '@shared/uikit/Typography';
import Alert from '@shared/components/molecules/Alert';
import { useMutation } from '@tanstack/react-query';
import { withdrawDuplicateCandidate } from '@shared/utils/api/jobs';
import InfoCard from '@shared/uikit/InfoCard';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import { useParams } from 'next/navigation';

const DuplicationVendorsPanel = () => {
  const { t } = useTranslation();
  const appDispatch = useGlobalDispatch();
  const { openConfirmDialog } = useOpenConfirm({
    variant: 'wideRightSideModal',
  });
  const params = useParams();
  const jobId = Number(params?.id);

  const { handleChangeParams } = useCustomParams();
  const [selectedId, setSelectedId] = useState<string | undefined>(undefined);
  const duplicationVendorsModal = useGlobalState('duplicationVendorsModal');
  const candidateId = duplicationVendorsModal?.id;

  const { mutate: withdrawDuplicateCandidateMutate } = useMutation({
    mutationFn: withdrawDuplicateCandidate,
    onSuccess: () => {
      handleChangeParams({ add: { currentEntityId: candidateId } });
      appDispatch({
        type: 'TOGGLE_CANDIDATE_MANAGER',
        payload: {
          isOpen: true,
          tab: 'notes',
          id: candidateId,
          enableNavigate: true,
        },
      });
      appDispatch({ type: 'CLOSE_DUPLICATION_VENDORS_PANEL' });
    },
  });

  const onContinue = () => {
    openConfirmDialog({
      title: t('continue'),
      message: (
        <Flex flexDir="column" className="gap-20">
          <Typography>{t('confirm_duplicate_vendor')}</Typography>
          <InfoCard
            hasLeftIcon
            leftIconProps={{
              name: 'exclamation-triangle',
              color: 'warning',
            }}
            classNames={{ wrapper: '!bg-warning_10 mt-[-8px]' }}
            label={t('confirm_duplicate_vendor_alert')}
          />
        </Flex>
      ),
      confirmButtonText: t('continue'),
      cancelButtonText: t('cancel'),
      confirmCallback: () => {
        withdrawDuplicateCandidateMutate({
          candidateIds: [candidateId!],
          jobId,
        });
      },
    });
  };

  const closeModal = () => {
    appDispatch({ type: 'CLOSE_DUPLICATION_VENDORS_PANEL' });
  };

  const onClose = (e?: RMouseEvent<HTMLElement, MouseEvent> | null) => {
    closeModal();
  };

  return (
    <FixedRightSideModal
      onBack={onClose}
      onClose={onClose}
      onClickOutside={onClose as any}
      isOpenAnimation
      wide
    >
      <ModalHeaderSimple title={t('candidate_duplication')} />
      <ModalBody className="p-20">
        <DuplicationVendorItem
          setSelectedId={setSelectedId}
          selectedId={selectedId}
        />
      </ModalBody>

      <ModalFooter>
        <Flex flexDir="row" className="gap-8">
          <Button
            fullWidth
            schema="secondary-dark"
            onClick={onClose}
            label={t('discard')}
          />

          <Button
            fullWidth
            schema="primary-blue"
            onClick={onContinue}
            label={t('continue')}
          />
        </Flex>
      </ModalFooter>
    </FixedRightSideModal>
  );
};

export default DuplicationVendorsPanel;
