import type { NormalizedJobParticipationModel } from '@shared/types/jobsProps';
import { getPipelineParticipations } from '@shared/utils/api/jobs';
import { QueryKeys } from '@shared/utils/constants';
import useInfiniteQuery from 'shared/utils/hooks/useInfiniteQuery';
import React, { useState } from 'react';
import Flex from '@shared/uikit/Flex';
import {
  useGlobalDispatch,
  useGlobalState,
} from '@shared/contexts/Global/global.provider';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import type {
  BECandidateSearchResult,
  CandidateFormData,
} from '@shared/types/candidates';
import {
  getCandidateById,
  getCandidateCompare,
  getCandidateSummary,
} from '@shared/utils/api/candidates';
import ObjectInfoCard from '@shared/components/molecules/ObjectInfoCard';
import cleanRepeatedWords from '@shared/utils/toolkit/cleanRepeatedWords';
import Divider from '@shared/uikit/Divider';
import Typography from '@shared/uikit/Typography';
import useTranslation from '@shared/utils/hooks/useTranslation';
import AvatarCard from '@shared/uikit/AvatarCard';
import RadioButton from '@shared/uikit/RadioButton';
import formatDate from '@shared/utils/toolkit/formatDate';
import Skeleton from '@shared/uikit/Skeleton';
import Button from '@shared/uikit/Button';
import classes from './DuplicationVendorItem.module.scss';

interface DuplicationVendorItemProps {
  selectedId?: number | undefined;
  setSelectedId: React.Dispatch<React.SetStateAction<number | undefined>>;
}

const DuplicationVendorItem = ({
  selectedId,
  setSelectedId,
}: DuplicationVendorItemProps) => {
  const { t } = useTranslation();
  const globalDispatch = useGlobalDispatch();
  const duplicationVendorsModal = useGlobalState('duplicationVendorsModal');
  const candidateId = duplicationVendorsModal?.id;
  const vendorSubmitted = duplicationVendorsModal?.vendorSubmitted;
  // const {  } = useGlobalState('compareModal');

  const { data: candidateCompare, isLoading: isLoadingCandidateCompare } =
    useReactQuery({
      action: {
        apiFunc: getCandidateCompare,
        key: [QueryKeys.getCandidateCompare, candidateId],
        params: {
          id: candidateId,
        },
      },
    });

  const selectedUsers = candidateCompare;

  const onCompare = () => {
    globalDispatch({
      type: 'TOGGLE_COMPARE_MODAL',
      payload: {
        open: true,
        selectedUsers,
      },
    });
  };

  const { data: candidate, isLoading: isLoadingCandidate } =
    useReactQuery<CandidateFormData>({
      action: {
        apiFunc: () => getCandidateById(candidateId!),
        key: [QueryKeys.getCandidate, candidateId],
      },
      config: {
        enabled: !!candidateId,
      },
    });

  return (
    <Flex flexDir="column" className=" gap-20">
      {isLoadingCandidate ? (
        <Skeleton className="h-[122px] w-full " />
      ) : (
        <ObjectInfoCard
          avatar={candidate?.profile?.croppedImageUrl}
          firstText={candidate?.profile?.fullName}
          secondText={
            candidate?.profile?.usernameAtSign ??
            (candidate?.profile?.email as any)?.value
          }
          thirdText={candidate?.profile?.occupation?.label}
          fourthText={cleanRepeatedWords(
            candidate?.profile?.location?.title || ''
          )}
          className="bg-gray_5 border border-solid !border-techGray_20 p-20 rounded"
        />
      )}
      <Divider />
      <Typography color="secondaryDisabledText" fontSize={15}>
        {t('duplication_vendors_title')}
      </Typography>

      {vendorSubmitted?.map((item) => (
        <Flex
          key={item?.pageInfo?.id}
          flexDir="row"
          className="justify-between bg-gray_5 border border-solid !border-techGray_20 p-20 rounded items-center cursor-pointer"
          onClick={() => setSelectedId(item?.pageInfo?.id)}
        >
          <Flex flexDir="row" className="gap-12 items-center">
            <AvatarCard
              avatarProps={{ isCompany: true, size: 'slg' }}
              data={{
                image: item?.pageInfo?.croppedImageUrl,
              }}
            />
            <Flex flexDir="column" className="gap-4">
              <Typography color="smoke_coal" fontSize={20} fontWeight={700}>
                {item?.pageInfo?.title}
              </Typography>
              <Typography
                color="colorIconForth2"
                fontSize={14}
                fontWeight={500}
              >
                {t('submitted_on')}:
              </Typography>
              <Typography color="primaryText" fontSize={14} fontWeight={400}>
                {formatDate(item?.pageInfo?.createdDate, 'DD/MM/YYYY hh:mmA')}
              </Typography>
            </Flex>
          </Flex>

          <RadioButton
            value={item?.pageInfo?.id === selectedId}
            style={classes.radioButton}
          />
        </Flex>
      ))}

      <Flex flexDir="row" className="gap-8 items-center">
        <Divider />
        <Typography color="primaryText" fontSize={12} fontWeight={500}>
          {t('or_lower')}
        </Typography>
        <Divider />
      </Flex>

      <Typography color="secondaryDisabledText" fontSize={15}>
        {t('duplication_vendors_subtitle')}
      </Typography>
      {!isLoadingCandidateCompare &&
        candidateCompare?.length &&
        candidateCompare?.length > 1 && (
          <Button
            variant="default"
            labelColor="brand"
            schema="semi-transparent"
            label={t('compare_candidates')}
            onClick={onCompare}
          />
        )}
    </Flex>
  );
};

export default DuplicationVendorItem;
