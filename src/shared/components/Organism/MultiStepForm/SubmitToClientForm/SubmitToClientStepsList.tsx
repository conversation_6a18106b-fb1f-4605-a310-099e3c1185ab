import React from 'react';
import useTranslation from '@shared/utils/hooks/useTranslation';
import EmptySearchResult from '@shared/components/Organism/EmptySearchResult';
import useSubmitToClientForm from './useSubmitToClientForm';
import SkeletonItems from './SubmitToClientSkeletonItems';
import { SearchableAsyncList } from '@shared/uikit/SearchableAsyncList';
import ItemComponent from '@shared/components/Organism/AsyncPickerModal/components/ItemComponent';
import IconButton from '@shared/uikit/Button/IconButton';
import CheckBox from '@shared/uikit/CheckBox';

const StepsList = () => {
  const { t } = useTranslation();

  const {
    stepKey: step,
    apiFunc,
    apiParams,
    placeHolder,
    queryName,
    onClickJob,
    onClickSuggestCompany,
    submitToClientFormState,
  } = useSubmitToClientForm();

  const content = (item: any) => {
    return step === 'client' ? (
      <ItemComponent
        key={item?.id}
        image={item?.imageUrl}
        title={item?.title}
        subTitle={item?.industryName}
      >
        <IconButton
          name="chevron-right"
          size="md20"
          color="brand"
          onClick={() => onClickSuggestCompany(item)}
          style={{ cursor: 'pointer' }}
        />
      </ItemComponent>
    ) : (
      <ItemComponent
        key={item?.id}
        image={item?.imageUrl}
        title={item?.title}
        subTitle={item?.subtitle}
        onClick={() => onClickJob(item)}
      >
        <CheckBox
          classNames={{ root: 'ml-auto' }}
          value={submitToClientFormState?.jobIds?.find((id) => id === item.id)}
        />
      </ItemComponent>
    );
  };

  return (
    <SearchableAsyncList
      name={queryName}
      variant="multi"
      params={apiParams}
      enableInfiniteScroll={step === 'client'}
      listItemsClassName="py-16"
      renderLoading={<SkeletonItems />}
      pageSize={step === 'client' ? 10 : undefined}
      renderEmpty={
        <EmptySearchResult
          title={t(emptyTitleText[step === 'client' ? 'client' : 'job'])}
          sectionMessage={t(
            emptyDescriptionText[step === 'client' ? 'client' : 'job']
          )}
          className="flex-1"
        />
      }
      renderItem={({ item }) => content(item)}
      apiFunc={apiFunc}
      hasSearch
      placeholder={placeHolder}
    />
  );
};

export default StepsList;

const emptyTitleText = {
  client: 'no_clients_found',
  job: 'no_jobs_found',
};
const emptyDescriptionText = {
  client: 'no_client_to_submit',
  job: 'no_job_to_link',
};
