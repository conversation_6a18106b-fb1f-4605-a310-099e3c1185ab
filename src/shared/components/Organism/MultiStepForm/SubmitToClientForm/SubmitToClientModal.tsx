import React from 'react';
import MultiStepForm from '../MultiStepForm';
import useSubmitToClientForm from './useSubmitToClientForm';
import { closeMultiStepForm } from '@shared/hooks/useMultiStepForm';

const SubmitToClientModal = () => {
  const { getHeaderProps, getStepHeaderProps, renderFooter, renderBody } =
    useSubmitToClientForm();

  return (
    <MultiStepForm
      totalSteps={2}
      getHeaderProps={getHeaderProps}
      getStepHeaderProps={getStepHeaderProps}
      renderBody={renderBody}
      renderFooter={renderFooter}
      onClose={() => closeMultiStepForm('submitToClientForm')}
      formName="submitToClientForm"
      isOpenAnimation
      enableReinitialize
    />
  );
};

export default SubmitToClientModal;
