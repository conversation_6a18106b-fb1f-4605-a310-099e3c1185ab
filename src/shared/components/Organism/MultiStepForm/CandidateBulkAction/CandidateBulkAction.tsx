'use client';

import React, { useState } from 'react';
import {
  closeMultiStepForm,
  useMultiStepFormState,
} from 'shared/hooks/useMultiStepForm';
import {
  updateBulkEmail,
  updateBulkNote,
  updateBulkTodo,
} from '@shared/utils/api/jobs';
import useTranslation from '@shared/utils/hooks/useTranslation';
import useResponseToast from '@shared/hooks/useResponseToast';
import { useCandidateBulkActionsStepTwo } from '@shared/components/Organism/MultiStepForm/CandidateBulkAction/useCandidateBulkActionsStepTwo';
import { useCandidateBulkActionsStepThree } from '@shared/components/Organism/MultiStepForm/CandidateBulkAction/useCandidateBulkActionsStepThree';
import { useCandidateBulkActionsStepFour } from '@shared/components/Organism/MultiStepForm/CandidateBulkAction/useCandidateBulkActionsStepFour';
import { useCandidateBulkActionsStepFive } from '@shared/components/Organism/MultiStepForm/CandidateBulkAction/useCandidateBulkActionsStepFive';
import { useCandidateBulkActionsStepSix } from '@shared/components/Organism/MultiStepForm/CandidateBulkAction/useCandidateBulkActionsStepSix';
import type { MultiStepFormProps } from '../MultiStepForm';
import MultiStepForm from '../MultiStepForm';
import { useCandidateBulkActionStepOne } from './useCandidateBulkActionStepOne';

type Props = {};
export type Method = 'google' | 'emails' | 'bulk' | undefined;

const SubmitVendor: React.FC<Props> = () => {
  const { t } = useTranslation();
  const { handleSuccess } = useResponseToast();

  const { data: candidateBulkAction } = useMultiStepFormState(
    'candidateBulkAction'
  );
  const initialStep = candidateBulkAction?.step === '1' ? 0 : 1;
  const [currentStep, setCurrentStep] = useState(1);

  const step1 = useCandidateBulkActionStepOne({ setCurrentStep });
  const step2 = useCandidateBulkActionsStepTwo();
  const step3 = useCandidateBulkActionsStepThree();
  const step4 = useCandidateBulkActionsStepFour();
  const step5 = useCandidateBulkActionsStepFive();
  const step6 = useCandidateBulkActionsStepSix();

  const steps = [...step1, ...step2, ...step3, ...step4, ...step5, ...step6];

  const onClose = () => closeMultiStepForm('candidateBulkAction');

  const getHeaderProps = getStepData('getHeaderProps', steps);
  const getStepHeaderProps = getStepData('getStepHeaderProps', steps);
  const renderBody = getStepData('renderBody', steps);
  const renderFooter = getStepData('renderFooter', steps);

  const apiPartials = {
    3: {
      apiFunc: updateBulkEmail,
      onSuccess: () => {
        handleSuccess({
          message: t('candidate_bulk_email_message'),
          title: t('candidate_bulk_email_title'),
        });
      },
      initialValues: {
        template: '',
      },
    },
    4: {
      apiFunc: updateBulkEmail,
      onSuccess: () => {
        handleSuccess({
          message: t('candidate_bulk_messages_message'),
          title: t('candidate_bulk_messages_title'),
        });
      },
    },
    5: {
      apiFunc: updateBulkNote,
      onSuccess: () => {
        handleSuccess({
          message: t('candidate_bulk_note_message'),
          title: t('candidate_bulk_note_title'),
        });
      },
    },
    6: {
      apiFunc: updateBulkTodo,
      onSuccess: () => {
        handleSuccess({
          message: t('candidate_bulk_todo_message'),
          title: t('candidate_bulk_todo_title'),
        });
      },
    },
  };

  return (
    <MultiStepForm
      wide
      initialStep={initialStep}
      apiFunc={apiPartials?.[currentStep]?.apiFunc}
      onSuccess={apiPartials?.[currentStep]?.onSuccess}
      initialValues={apiPartials?.[currentStep]?.initialValues}
      // initialValues={{
      //   message: ' <p>test</p>',
      // }}
      showConfirm={false}
      enableReinitialize
      totalSteps={steps?.length}
      getHeaderProps={getHeaderProps}
      renderBody={renderBody}
      renderFooter={renderFooter}
      getStepHeaderProps={getStepHeaderProps}
      onClose={onClose}
      isOpenAnimation
      formName="candidateBulkAction"
    />
  );
};

export default SubmitVendor;

function getStepData<T extends keyof MultiStepFormProps>(
  key: T,
  data: MultiStepFormProps[]
): MultiStepFormProps[T] {
  return ({ step, ...rest }: any) => data?.[step]?.[key]?.({ step, ...rest });
}
