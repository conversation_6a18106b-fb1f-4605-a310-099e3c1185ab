import { useState } from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import DynamicFormBuilder from '@shared/uikit/Form/DynamicFormBuilder';
import { getAllTemplates } from '@shared/utils/api/template';
import { TwoButtonFooter } from '../ProfileSections/Components/TwoButtonFooter';
import type { MultiStepFormProps } from '../MultiStepForm';

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
  renderFooter: Exclude<MultiStepFormProps['renderFooter'], undefined>;
};

export function useCandidateBulkActionsStepThree(): SingleDataItem[] {
  const { t } = useTranslation();
  const [formData, setFormData] = useState<{
    item: any;
    subject: string;
    body: string;
    fieldIds: number[];
  } | null>(null);

  console.log('formData', formData?.body);

  const getHeaderProps: SingleDataItem['getHeaderProps'] = ({ setStep }) => ({
    title: t('email'),
    hideBack: false,
    noCloseButton: true,
    backButtonProps: {
      onClick: () => setStep(0),
    },
  });

  const renderFooter: SingleDataItem['renderFooter'] = ({ setStep }) => (
    <TwoButtonFooter
      submitLabel={t('send')}
      secondaryButtonLabel={t('discard')}
      // onSubmitClick={() => setStep((prev) => prev + 1)}
      secondaryButtonOnClick={() => setStep(0)}
    />
  );

  const data: Array<SingleDataItem> = [
    {
      stepKey: '3',
      getHeaderProps,

      renderBody: ({ setFieldValue }) => (
        <DynamicFormBuilder
          className="gap-12"
          groups={[
            {
              label: t('template'),
              required: true,
              name: 'template',
              cp: 'asyncAutoComplete',
              maxLength: 100,
              apiFunc: getAllTemplates,
              normalizer: (data: any) =>
                data?.content?.map((item: any) => ({
                  value: item.id,
                  label: item.title,
                  ...item,
                })),
              onChange: (item: any) => {
                console.log('itemmmm', item);
                setFormData({
                  item: { value: item.id, label: item.title },
                  subject: item?.subject,
                  body: item?.message,
                  fieldIds: item?.fileIds,
                });
                setFieldValue('template', item?.id);
                setFieldValue('message', item?.message);

                // const language = item?.language;
                // setTimeout(() => setFieldTouched('language', true), 0);
              },
              value: formData?.item,
              // normalizer: lookupResponseNormalizer,
              // wrapStyle: parentClasses.formItem,
            },
            {
              name: 'subject',
              cp: 'input',
              label: t('subject'),
              // wrapStyle: '!basis-0',
              required: false,
              disabled: true,
              value: formData?.subject,
            },
            {
              name: 'message',
              cp: 'richtext',
              label: t(`message`),
              showEmoji: false,
              wrapStyle: '!basis-0 !flex-1',
              className: 'flex-1 !max-h-none',
              disabled: true,
              required: false,
              readonly: true,
              value: formData?.body,
              defaultValue: formData?.body + formData?.body,

              // key: richTextKey,
              // ref: richTextRef,
              // topComponents: (
              //   <Button
              //     label={t('paste_previous_one')}
              //     className="absolute right-0 translate-y-[-20px]"
              //     schema="transparent-brand"
              //     onClick={getPreviousCL}
              //     disabled={isPasted}
              //   />
              // ),
            },
            {
              name: 'attachmentFileIds',
              cp: 'attachmentPicker',
              value: formData?.fieldIds,
              // wrapStyle: 'responsive-margin-top',
              label: t('attachment'),
              required: false,
              visibleOptionalLabel: false,
            },
          ].filter(Boolean)}
        />
      ),

      renderFooter,
    },
  ];

  return data;
}
