import useTranslation from 'shared/utils/hooks/useTranslation';
import DynamicFormBuilder from '@shared/uikit/Form/DynamicFormBuilder';
import { TwoButtonFooter } from '../ProfileSections/Components/TwoButtonFooter';
import type { MultiStepFormProps } from '../MultiStepForm';

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
  renderFooter: Exclude<MultiStepFormProps['renderFooter'], undefined>;
};

export function useCandidateBulkActionsStepFive(): SingleDataItem[] {
  const { t } = useTranslation();

  const visibilityOptions = [
    {
      value: 'Team',
      label: t('Team'),
    },
    {
      value: 'Only me',
      label: t('only_me'),
    },
  ];

  const getHeaderProps: SingleDataItem['getHeaderProps'] = ({ setStep }) => ({
    title: t('note'),
    hideBack: false,
    noCloseButton: true,
    backButtonProps: {
      onClick: () => setStep(0),
    },
  });

  const renderFooter: SingleDataItem['renderFooter'] = ({ setStep }) => (
    <TwoButtonFooter
      submitLabel={t('send')}
      secondaryButtonLabel={t('discard')}
      onSubmitClick={() => setStep((prev) => prev + 1)}
      secondaryButtonOnClick={() => setStep(0)}
    />
  );

  const data: Array<SingleDataItem> = [
    {
      stepKey: '3',
      getHeaderProps,

      renderBody: () => (
        <DynamicFormBuilder
          className="gap-12"
          groups={[
            {
              formGroup: {
                color: 'smoke_coal',
                title: t('visibility'),
              },
              label: t('visibility'),
              cp: 'radioGroup',
              name: 'visibility',
              divider: {
                className: 'my-20',
              },
              options: visibilityOptions,
              getValue: () => 'ALL',
              schema: 'semi-transparent',
              isDefaultValue: 'ALL',
            },
            {
              name: 'body',
              cp: 'richtext',
              label: t(`Write note`),
              showEmoji: false,
              wrapStyle: '!basis-0 !flex-1',
              className: 'flex-1 !max-h-none',
              // key: richTextKey,
              // ref: richTextRef,
              // topComponents: (
              //   <Button
              //     label={t('paste_previous_one')}
              //     className="absolute right-0 translate-y-[-20px]"
              //     schema="transparent-brand"
              //     onClick={getPreviousCL}
              //     disabled={isPasted}
              //   />
              // ),
            },
            {
              name: 'fileIds',
              cp: 'attachmentPicker',
              // value: '',
              // wrapStyle: 'responsive-margin-top',
              label: t('attachment'),
              visibleOptionalLabel: false,
            },
          ].filter(Boolean)}
        />
      ),

      renderFooter,
    },
  ];

  return data;
}
