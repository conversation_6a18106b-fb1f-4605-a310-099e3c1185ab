import useTranslation from 'shared/utils/hooks/useTranslation';
import DynamicFormBuilder from '@shared/uikit/Form/DynamicFormBuilder';
import { schedulesDb } from '@shared/utils/constants';
import { TwoButtonFooter } from '../ProfileSections/Components/TwoButtonFooter';
import type { MultiStepFormProps } from '../MultiStepForm';

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
  renderFooter: Exclude<MultiStepFormProps['renderFooter'], undefined>;
};

export function useCandidateBulkActionsStepSix(): SingleDataItem[] {
  const { t } = useTranslation();

  const visibilityOptions = [
    {
      value: 'Team',
      label: t('Team'),
    },
    {
      value: 'Only me',
      label: t('only_me'),
    },
  ];

  const getHeaderProps: SingleDataItem['getHeaderProps'] = ({ setStep }) => ({
    title: t('todo`'),
    hideBack: false,
    noCloseButton: true,
    backButtonProps: {
      onClick: () => setStep(0),
    },
  });

  const renderFooter: SingleDataItem['renderFooter'] = ({ setStep }) => (
    <TwoButtonFooter
      submitLabel={t('send')}
      secondaryButtonLabel={t('discard')}
      onSubmitClick={() => setStep((prev) => prev + 1)}
      secondaryButtonOnClick={() => setStep(0)}
    />
  );

  const data: Array<SingleDataItem> = [
    {
      stepKey: '3',
      getHeaderProps,

      renderBody: () => (
        <DynamicFormBuilder
          className="gap-12"
          groups={[
            {
              name: 'title',
              cp: 'input',
              maxLength: 30,
              label: t('title'),
              required: true,
            },
            {
              name: 'description',
              cp: 'richtext',
              label: t(`description`),
              showEmoji: false,
              wrapStyle: '!basis-0 !flex-1',
              className: 'flex-1 !max-h-none',
              // key: richTextKey,
              // ref: richTextRef,
              // topComponents: (
              //   <Button
              //     label={t('paste_previous_one')}
              //     className="absolute right-0 translate-y-[-20px]"
              //     schema="transparent-brand"
              //     onClick={getPreviousCL}
              //     disabled={isPasted}
              //   />
              // ),
            },
            {
              name: 'startDate',
              cp: 'datePicker',
              closeOnSelect: true,
              // calendarClassName: classes.datepicker,
              rightIconClassName: '!bg-transparent',
              variant: 'input',
              label: t('start_date'),
            },
            {
              name: 'startTime',
              cp: 'dropdownSelect',
              rightIconClassName: '!bg-transparent',
              options: schedulesDb.timeOptions,
              label: t('start_time'),
              doNotUseTranslation: true,
              rightIconProps: { name: 'clock' },
              // displayName: values.startTime?.label,
            },
            {
              name: 'endDate',
              cp: 'datePicker',
              closeOnSelect: true,
              // calendarClassName: classes.datepicker,
              rightIconClassName: '!bg-transparent',
              variant: 'input',
              label: t('end_date'),
            },
            {
              name: 'endTime',
              cp: 'dropdownSelect',
              rightIconClassName: '!bg-transparent',
              options: schedulesDb.timeOptionsEnd,
              label: t('end_time'),
              doNotUseTranslation: true,
              rightIconProps: { name: 'clock' },
              // displayName: values.endTime?.label,
            },
          ].filter(Boolean)}
        />
      ),

      renderFooter,
    },
  ];

  return data;
}
