'use client';

import React, { useState } from 'react';
import {
  closeMultiStepForm,
  useMultiStepFormState,
} from 'shared/hooks/useMultiStepForm';
import type { MultiStepFormProps } from '../MultiStepForm';
import MultiStepForm from '../MultiStepForm';
import { useSubmitJobStepOne } from './useSubmitJobStepOne';
import { useSubmitJobStepTwo } from './useSubmitJobStepTwo';

type Props = {};
export type Method = 'google' | 'emails' | 'bulk' | undefined;

const SubmitJob: React.FC<Props> = () => {
  const { data: submitJob } = useMultiStepFormState('submitJob');
  const initialStep = submitJob?.step === '1' ? 0 : 1;
  const [jobId, setJobId] = useState<number | undefined>(undefined);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const data = useSubmitJobStepOne({ setJobId, setIsSubmitted });
  const sendStep = useSubmitJobStepTwo({ jobId, isSubmitted });

  const steps = [...data, ...sendStep];

  const onClose = () => closeMultiStepForm('submitJob');

  const getHeaderProps = getStepData('getHeaderProps', steps);
  const getStepHeaderProps = getStepData('getStepHeaderProps', steps);
  const renderBody = getStepData('renderBody', steps);
  const renderFooter = getStepData('renderFooter', steps);

  return (
    <MultiStepForm
      wide
      initialStep={initialStep}
      showConfirm={false}
      enableReinitialize
      totalSteps={steps?.length}
      getHeaderProps={getHeaderProps}
      renderBody={renderBody}
      renderFooter={renderFooter}
      getStepHeaderProps={getStepHeaderProps}
      onClose={onClose}
      isOpenAnimation
      formName="submitJob"
    />
  );
};

export default SubmitJob;

function getStepData<T extends keyof MultiStepFormProps>(
  key: T,
  data: MultiStepFormProps[]
): MultiStepFormProps[T] {
  return ({ step, ...rest }: any) => data?.[step]?.[key]?.({ step, ...rest });
}
