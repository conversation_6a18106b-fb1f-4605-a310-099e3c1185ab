import type { NextRequest } from 'next/server';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: any }> }
) {
  const { searchParams } = request.nextUrl;
  const { slug } = await params;

  const url = new URL('/', 'https://storage.googleapis.com');

  url.pathname = ['lobox_public_resumes', ...slug].join('/');

  if (searchParams) {
    url.search = searchParams.toString();
  }
  // TODO: improve this functionality with readable stream ans pipe to response
  const res = await fetch(url).then((response) => response.body);
  return new Response(res);
}
